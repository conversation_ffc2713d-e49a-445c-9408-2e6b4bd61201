<template>
    <div class="min-h-screen bg-gray-50 dark:bg-black dark:text-gray-200">
        <div class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col items-start mb-8">
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Tickets</h1>
                <p>Manage your event tickets generated</p>
            </div>

            <div class="mb-6 flex bg-white shadow-sm p-3 flex-wrap justify-between items-center gap-4">
                <div class="max-w-md flex items-center space-x-2">
                    <div class="relative">
                        <div v-if="eventsLoading" class="flex items-center space-x-2 py-2">
                            <div class="animate-spin h-5 w-5 border-2 border-red-500 rounded-full border-t-transparent">
                            </div>
                            <span class="text-gray-500 dark:text-gray-400">Loading events...</span>
                        </div>
                        <div v-else-if="events.length === 0"
                            class="py-2 px-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded">
                            <div class="flex items-center">
                                <Icon icon="heroicons:exclamation-triangle" class="w-5 h-5 mr-2" />
                                <span>No events found. Please create an event first.</span>
                            </div>
                        </div>
                        <Combobox v-else v-model="selectedEvent">
                            <div class="relative">
                                <div
                                    class="relative w-full cursor-default overflow-hidden border border-gray-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 text-left sm:text-sm">
                                    <div class="absolute bg-red-600 text-white inset-y-0 left-0 flex items-center px-2">
                                        <Icon icon="fluent:ticket-diagonal-24-filled" class="w-5 h-5" />
                                    </div>
                                    <ComboboxInput v-model="query" placeholder="Search events..."
                                        class="w-full border-none py-2 pl-10 pr-10 text-gray-900 dark:text-white focus:ring-0 focus:outline-none bg-transparent"
                                        :display-value="(event: any) => event?.title || ''" />
                                    <ComboboxButton class="absolute inset-y-0 right-0 flex items-center pr-2">
                                        <ChevronDownIcon class="h-5 w-5 text-gray-400 dark:text-gray-500"
                                            aria-hidden="true" />
                                    </ComboboxButton>
                                </div>

                                <ComboboxOptions v-if="filteredEvents.length"
                                    class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded bg-white dark:bg-zinc-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-zinc-700 focus:outline-none sm:text-sm">
                                    <ComboboxOption v-for="event in filteredEvents" :key="event.id" :value="event"
                                        v-slot="{ active, selected }">
                                        <div :class="[
                                            active ? 'bg-red-600 text-white' : 'text-gray-900 dark:text-gray-200',
                                            'relative cursor-default select-none py-2 pl-10 pr-4'
                                        ]">
                                            <span
                                                :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']">
                                                {{ event.title }}
                                            </span>
                                            <span v-if="selected" :class="[
                                                active ? 'text-white' : 'text-red-600',
                                                'absolute inset-y-0 left-0 flex items-center pl-3'
                                            ]">
                                                <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                            </span>
                                        </div>
                                    </ComboboxOption>
                                </ComboboxOptions>

                                <div v-else-if="query"
                                    class="absolute z-10 mt-1 w-full bg-white dark:bg-zinc-800 py-2 px-3 text-gray-500 dark:text-gray-400 text-sm border border-gray-200 dark:border-zinc-700 shadow-lg">
                                    No events found matching "{{ query }}".
                                </div>
                            </div>
                        </Combobox>
                    </div>
                    <div class="relative flex-grow max-w-md">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400 dark:text-gray-500" />
                        </div>
                        <input type="text" v-model="searchUuid" placeholder="Search by Ticket UUID"
                            class="block w-full py-2 pl-10 pr-3 bg-white dark:bg-zinc-800 text-gray-900 dark:text-white focus:outline-none focus:ring-0"
                            @keyup.enter="searchTickets" @input="onSearchInput" />
                    </div>
                </div>

                <div class="flex items-center space-x-4">

                <div class="flex items-center">
                    <Popover class="relative">
                        <PopoverButton
                            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-700 font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-800 focus:outline-none focus:ring-0">
                            <Icon icon="mynaui:filter-solid" class="w-5 h-5 mr-2" />
                            Filters
                        </PopoverButton>

                        <transition enter-active-class="transition ease-out duration-200"
                            enter-from-class="opacity-0 translate-y-1" enter-to-class="opacity-100 translate-y-0"
                            leave-active-class="transition ease-in duration-150"
                            leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 translate-y-1">
                            <PopoverPanel
                                class="absolute right-0 z-10 mt-2 w-96 origin-top-right bg-white dark:bg-zinc-800 shadow-lg ring-0 ring-black ring-opacity-5 dark:ring-zinc-700 focus:outline-none">
                                <div class="p-4 space-y-4">
                                    <h4 class="text-lg font-bold text-black dark:text-white">Filter Tickets</h4>

                                    <div class="w-full">
                                        <label class="block text-base font-semibold mb-2 dark:text-gray-300">Date
                                            Range</label>
                                        <div class="w-full">
                                            <datepicker required position="left" @cleared="cleared"
                                                placeholder="select start & end date" :range="true" format="dd/MM/yyyy"
                                                input-class-name="datepicker dark:bg-zinc-800 dark:text-white dark:border-zinc-700"
                                                v-model="dateRange" />
                                        </div>
                                    </div>

                                    <div>
                                        <label
                                            class="block text-base font-semibold mb-2 dark:text-gray-300">Status</label>
                                        <Listbox v-model="filters.status">
                                            <div class="relative">
                                                <ListboxButton
                                                    class="relative w-full border border-gray-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 text-left sm:text-sm py-2 px-3 dark:text-white">
                                                    <span>{{ statusLabel(filters.status) }}</span>
                                                </ListboxButton>

                                                <transition leave-active-class="transition ease-in duration-100"
                                                    leave-from-class="opacity-100" leave-to-class="opacity-0">
                                                    <ListboxOptions
                                                        class="absolute z-10 -mt-4 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-zinc-800 shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-zinc-700 focus:outline-none sm:text-sm">
                                                        <ListboxOption value="">
                                                            <li
                                                                class="cursor-pointer select-none relative py-2 px-4 hover:bg-gray-100 dark:hover:bg-zinc-800 dark:text-white">
                                                                All Statuses
                                                            </li>
                                                        </ListboxOption>
                                                        <ListboxOption value="scanned">
                                                            <li
                                                                class="cursor-pointer select-none relative py-2 px-4 hover:bg-gray-100 dark:hover:bg-zinc-800 dark:text-white">
                                                                Scanned
                                                            </li>
                                                        </ListboxOption>
                                                        <ListboxOption value="unscanned">
                                                            <li
                                                                class="cursor-pointer select-none relative py-2 px-4 hover:bg-gray-100 dark:hover:bg-zinc-800 dark:text-white">
                                                                Unscanned
                                                            </li>
                                                        </ListboxOption>
                                                    </ListboxOptions>
                                                </transition>
                                            </div>
                                        </Listbox>
                                    </div>

                                    <div class="flex justify-end space-x-2 pt-2">
                                        <button @click="resetFilters"
                                            class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-zinc-700 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-800">
                                            Reset
                                        </button>
                                        <button @click="applyFilters"
                                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </PopoverPanel>
                        </transition>
                    </Popover>
                </div>

                <div class="flex items-center space-x-2">
                    <json-excel v-if="selectedEvent && formattedTickets.length > 0"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-700 font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-800 focus:outline-none focus:ring-0"
                        :data="exportData" :fields="exportFields"
                        :name="`tickets-${selectedEvent?.slug || 'event'}-${dayjs().format('YYYY-MM-DD')}.xlsx`"
                        :before-generate="beforeExport" :before-finish="onExportDone">
                        <img src="@/assets/icons/excel.png" alt="Excel" class="h-5 w-5 mr-2" />
                        Export Excel
                    </json-excel>
                    <button v-else @click="$toast.error('Please select an event with tickets first')"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-700 font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-800 focus:outline-none focus:ring-0">
                        <img src="@/assets/icons/excel.png" alt="Excel" class="h-5 w-5 mr-2" />
                        Export Excel
                    </button>
                    <button @click="downloadPdf"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-zinc-700 font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-800 focus:outline-none focus:ring-0">
                        <img src="@/assets/icons/pdf.png" alt="PDF" class="h-5 w-5 mr-2" />
                        Download PDF
                    </button>
                </div>
                </div>
            </div>

            <!-- Selected Filters Display -->
            <div v-if="hasActiveFilters" class="mb-4 bg-white dark:bg-zinc-800 p-4 shadow-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Active Filters:</span>
                        <div class="flex flex-wrap gap-2">
                            <span v-if="filters.status"
                                class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Status: {{ statusLabel(filters.status) }}
                                <button @click="clearStatusFilter" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-red-400 hover:text-red-600">
                                    <Icon icon="heroicons:x-mark" class="w-3 h-3" />
                                </button>
                            </span>
                            <span v-if="filters.dateFrom && filters.dateTo"
                                class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                Date: {{ dayjs(filters.dateFrom).format('DD/MM/YYYY') }} - {{ dayjs(filters.dateTo).format('DD/MM/YYYY') }}
                                <button @click="clearDateFilter" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-blue-400 hover:text-blue-600">
                                    <Icon icon="heroicons:x-mark" class="w-3 h-3" />
                                </button>
                            </span>
                            <span v-if="searchUuid"
                                class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Search: {{ searchUuid }}
                                <button @click="clearSearchFilter" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-green-400 hover:text-green-600">
                                    <Icon icon="heroicons:x-mark" class="w-3 h-3" />
                                </button>
                            </span>
                        </div>
                    </div>
                    <button @click="clearAllFilters"
                        class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-zinc-700 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700">
                        <Icon icon="heroicons:x-mark" class="w-4 h-4 mr-1" />
                        Clear All
                    </button>
                </div>
            </div>

            <div v-if="selectedEvent" class="bg-white dark:bg-zinc-800">
                <div v-if="loading" class="py-20 flex justify-center">
                    <CoreLoader />
                </div>
                <div v-else-if="tickets && tickets.length === 0"
                    class="py-20 text-center text-gray-500 dark:text-gray-400">
                    No tickets found for this event
                </div>
                <div v-else>
                    <Datatable :headers="headers" :items="formattedTickets"
                        table-class-name="dark:bg-zinc-800 dark:text-white" :rows-items="[5, 10, 25, 50, 100]"
                        @page-change="onPageChange" v-model:server-options="serverOptions"
                        :server-items-length="pagination.total" show-index buttons-pagination alternate-stripe
                        theme-color="#dc2626">
                        <template #loading>
                            <CoreLoader />
                        </template>
                        <template #item-status="{ status }">
                            <div class="flex items-center">
                                <span
                                    class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-900 dark:bg-zinc-800 dark:text-gray-300 border-r border-gray-200 dark:border-zinc-700">
                                    Unsold
                                </span>
                                <span :class="[
                                    'inline-flex items-center px-2.5 py-0.5 text-xs font-medium',
                                    status === 'scanned'
                                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                ]">
                                    {{ status === 'scanned' ? 'Scanned' : 'Unscanned' }}
                                </span>
                            </div>
                        </template>
                        <template #item-actions="item">
                            <button @click="scanTicket(item)" :disabled="item.status === 'scanned'" :class="[
                                'inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium',
                                item.status === 'scanned'
                                    ? 'bg-gray-100 dark:bg-zinc-800 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                    : 'bg-red-600 text-white hover:bg-red-700'
                            ]">
                                <QrCodeIcon class="h-4 w-4 mr-1" />
                                Scan
                            </button>
                        </template>
                    </Datatable>
                </div>
            </div>
            <div v-else
                class="bg-white dark:bg-zinc-800 shadow overflow-hidden sm:rounded-md p-8 text-center text-gray-500 dark:text-gray-400 border dark:border-zinc-800">
                Please select an event to view tickets.
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions,
    Listbox,
    ListboxButton,
    ListboxOption,
    ListboxOptions,
    Popover,
    PopoverButton,
    PopoverPanel
} from '@headlessui/vue';
import {
    ChevronDownIcon,
    CheckIcon,
    QrCodeIcon,
    MagnifyingGlassIcon,
} from '@heroicons/vue/20/solid';
import { ref, watch, onMounted, computed } from 'vue';
import type { EventItem } from '@/types';
import type { EventsResponse } from '@/types/api';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@/utils/dateformat';
import JsonExcel from 'vue3-json-excel';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

definePageMeta({
    middleware: ['auth'],
    layout: 'dashboard'
});

interface Ticket {
    id: number;
    uuid: string;
    user_ticket: any;
    email: string;
    purchaseDate: string;
    scanned: number;
    created_at: string;
}

interface PaginationData {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface TicketResponse {
    data: Ticket[];
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

const httpClient = useHttpClient();
const events = ref<EventItem[]>([]);
const { $toast }: any = useNuxtApp();
const currentEventPage = ref<number>(1);
const itemsPerPage = ref<number>(50);
const totalEvents = ref<number>(0);
const selectedEvent = ref<EventItem | null>(null);
const tickets = ref<Ticket[]>([]);
const loading = ref(false);
const eventsLoading = ref(true);
const searchUuid = ref('');
const query = ref('');
const dateRange = ref([]);
const searchTimeout = ref<NodeJS.Timeout | null>(null);

const cleared = () => {
    dateRange.value = [];
    filters.value.dateFrom = '';
    filters.value.dateTo = '';
};

const statusLabel = (status: string) => {
    if (status === 'scanned') return 'Scanned'
    if (status === 'unscanned') return 'Unscanned'
    return 'All Statuses'
}

const filteredEvents = computed(() => {
    if (!query.value) return events.value
    return events.value.filter(event =>
        event.title.toLowerCase().includes(query.value.toLowerCase())
    )
});

const hasActiveFilters = computed(() => {
    return !!(filters.value.status || filters.value.dateFrom || filters.value.dateTo || searchUuid.value);
});

const formattedTickets = computed(() => {
    return tickets.value && tickets.value.length > 0 ?
        tickets.value.map((ticket) => {
            return {
                id: ticket.id,
                uuid: ticket.uuid,
                customer: ticket.user_ticket !== null ? ticket.user_ticket.name : "Not assigned",
                email: ticket.user_ticket !== null ? ticket.user_ticket.name : "Not assigned",
                purchaseDate: dayjs(ticket.created_at).format(DATE_FORMAT.FULL),
                status: ticket.scanned === 1 ? 'scanned' : 'unscanned',
            }
        }) : []
})

const pagination = ref<PaginationData>({
    current_page: 1,
    from: 0,
    last_page: 1,
    per_page: 50,
    to: 0,
    total: 0,
    links: []
});

const serverOptions = ref({
    page: pagination.value.current_page,
    rowsPerPage: pagination.value.per_page,
});

const filters = ref({
    dateFrom: '',
    dateTo: '',
    status: ''
});

const headers = [
    { text: 'TICKET UUID', value: 'uuid' },
    { text: 'CUSTOMER', value: 'customer' },
    { text: 'EMAIL ADDRESS', value: 'email' },
    { text: 'PURCHASE DATE', value: 'purchaseDate' },
    { text: 'STATUS', value: 'status' },
    { text: 'ACTIONS', value: 'actions' }
];

watch(serverOptions, (newOptions, oldOptions) => {
    if (newOptions.page !== oldOptions.page) {
        fetchTicketsForEvent(selectedEvent.value?.id || 0, newOptions.page);
    }
});

const fetchTicketsForEvent = async (eventId: number, page = 1, searchQuery = '', filterParams = {}) => {
    if (!eventId) {
        $toast.error('No event selected');
        return [];
    }

    try {
        let url = `${ENDPOINTS.EVENTS.TICKETS}/${eventId}?page=${page}&per_page=${pagination.value.per_page}`;

        if (searchQuery) {
            url += `&search=${searchQuery}`;
        }

        if (filterParams) {
            Object.entries(filterParams).forEach(([key, value]) => {
                if (value) {
                    url += `&${key}=${value}`;
                }
            });
        }

        const response = await httpClient.get<TicketResponse>(url);

        if (response && response.data) {
            tickets.value = response.data;

            // Update pagination data
            Object.assign(pagination.value, {
                current_page: response.current_page || 1,
                from: response.from || 0,
                last_page: response.last_page || 1,
                per_page: response.per_page || pagination.value.per_page,
                to: response.to || 0,
                total: response.total || 0,
                links: response.links || []
            });

            return response.data;
        } else {
            tickets.value = [];
            return [];
        }
    } catch (error) {
        console.error('Error fetching tickets:', error);
        $toast.error('Failed to load tickets. Please try again.');
        tickets.value = [];
        return [];
    }
};

const loadTickets = async () => {
    if (!selectedEvent.value) {
        tickets.value = [];
        return;
    }

    try {
        loading.value = true;
        await fetchTicketsForEvent(selectedEvent.value.id, pagination.value.current_page);
    } catch (error) {
        console.error('Error loading tickets:', error);
        $toast.error('Failed to load tickets. Please try again.');
        tickets.value = [];
    } finally {
        loading.value = false;
    }
};

const onPageChange = async (page: number) => {
    if (page < 1 || page > pagination.value.last_page) return;

    if (!selectedEvent.value) return;
    const filterParams = {
        date_from: filters.value.dateFrom,
        date_to: filters.value.dateTo,
        status: filters.value.status
    };

    await fetchTicketsForEvent(selectedEvent.value.id, page, searchUuid.value, filterParams);
};

const scanTicket = async (ticket: Ticket) => {
    if (ticket.scanned === 1) return;

    try {
        loading.value = true;
        await httpClient.post(`${ENDPOINTS.EVENTS.SEARCH}/${ticket.id}`);
        await loadTickets();

        $toast.success(`Ticket ${ticket.uuid} scanned successfully!`);
    } catch (error) {
        console.error('Error scanning ticket:', error);
        $toast.error('Failed to scan ticket. Please try again.');
    } finally {
        loading.value = false;
    }
};

const searchTickets = async () => {
    if (!selectedEvent.value) return;
    pagination.value.current_page = 1;
    const filterParams = {
        date_from: filters.value.dateFrom,
        date_to: filters.value.dateTo,
        status: filters.value.status
    };
    await fetchTicketsForEvent(selectedEvent.value.id, 1, searchUuid.value, filterParams);
};

const resetFilters = () => {
    filters.value = {
        dateFrom: '',
        dateTo: '',
        status: ''
    };
    dateRange.value = [];
};

const onSearchInput = () => {
    // Debounce search to avoid too many API calls
    if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
    }
    searchTimeout.value = setTimeout(() => {
        searchTickets();
    }, 500);
};

const clearStatusFilter = async () => {
    filters.value.status = '';
    await applyFilters();
};

const clearDateFilter = async () => {
    filters.value.dateFrom = '';
    filters.value.dateTo = '';
    dateRange.value = [];
    await applyFilters();
};

const clearSearchFilter = async () => {
    searchUuid.value = '';
    await searchTickets();
};

const clearAllFilters = async () => {
    filters.value = {
        dateFrom: '',
        dateTo: '',
        status: ''
    };
    dateRange.value = [];
    searchUuid.value = '';
    await searchTickets();
};

const applyFilters = async () => {
    if (!selectedEvent.value) return;
    pagination.value.current_page = 1;
    const filterParams = {
        date_from: filters.value.dateFrom,
        date_to: filters.value.dateTo,
        status: filters.value.status
    };

    await fetchTicketsForEvent(selectedEvent.value.id, 1, searchUuid.value, filterParams);
};

const exportFields = computed(() => ({
    'Ticket UUID': 'uuid',
    'Customer': 'customer',
    'Email': 'email',
    'Purchase Date': 'purchaseDate',
    'Status': 'status'
}));

const exportData = computed(() => {
    return formattedTickets.value.map(ticket => ({
        uuid: ticket.uuid,
        customer: ticket.customer,
        email: ticket.email,
        purchaseDate: ticket.purchaseDate,
        status: ticket.status === 'scanned' ? 'Scanned' : 'Unscanned'
    }));
});

const beforeExport = (): void => {
    $toast.info('Preparing Excel export...');
};

const onExportDone = (): void => {
    $toast.success('Excel file exported successfully');
};



const downloadPdf = async () => {
    if (!selectedEvent.value) {
        $toast.error('Please select an event first');
        return;
    }

    if (formattedTickets.value.length === 0) {
        $toast.error('No tickets available to export');
        return;
    }

    try {
        $toast.info('Generating PDF...');

        const doc = new jsPDF();
        const tableColumn = ["Ticket UUID", "Customer", "Email", "Purchase Date", "Status"];
        const tableRows: any[] = [];

        formattedTickets.value.forEach(ticket => {
            const ticketData = [
                ticket.uuid,
                ticket.customer,
                ticket.email,
                ticket.purchaseDate,
                ticket.status === 'scanned' ? 'Scanned' : 'Unscanned'
            ];
            tableRows.push(ticketData);
        });

        doc.setFontSize(18);
        doc.setTextColor(220, 38, 38);
        doc.text('EventaHub', 14, 15);

        doc.setFontSize(15);
        doc.setTextColor(0, 0, 0);
        doc.text(`Tickets for: ${selectedEvent.value.title}`, 14, 25);

        doc.setFontSize(11);
        doc.setTextColor(100, 100, 100);
        doc.text(`Generated on: ${dayjs().format('MMMM D, YYYY')} at ${dayjs().format('h:mm A')}`, 14, 32);
        doc.text(`Total Tickets: ${formattedTickets.value.length}`, 14, 38);

        if (filters.value.status) {
            doc.text(`Filter: ${statusLabel(filters.value.status)}`, 14, 44);
        }

        autoTable(doc, {
            head: [tableColumn],
            body: tableRows,
            startY: 50,
            theme: 'grid',
            styles: {
                fontSize: 10,
                cellPadding: 3,
                overflow: 'linebreak',
            },
            headStyles: {
                fillColor: [220, 38, 38],
                textColor: 255,
                fontStyle: 'bold',
            },
            alternateRowStyles: {
                fillColor: [245, 245, 245]
            }
        });

        doc.save(`tickets-${selectedEvent.value.slug}-${dayjs().format('YYYY-MM-DD')}.pdf`);
        $toast.success('PDF downloaded successfully!');
    } catch (error) {
        console.error('Error downloading PDF:', error);
        $toast.error('Failed to download PDF. Please try again.');
    }
};

const fetchEvents = async (): Promise<void> => {
    try {
        eventsLoading.value = true;
        const response = await httpClient.get<EventsResponse>(
            `${ENDPOINTS.EVENTS.USER}?per_page=${itemsPerPage.value}&page=${currentEventPage.value}`
        );

        if (response && response.events) {
            events.value = response.events.data || [];
            totalEvents.value = response.events.total || 0;

            if (events.value.length > 0 && !selectedEvent.value) {
                selectedEvent.value = events.value[0];
            }
        } else {
            events.value = [];
            totalEvents.value = 0;
            $toast.warning('No events found. Please create an event first.');
        }
    } catch (error) {
        events.value = [];
        totalEvents.value = 0;
        $toast.error('An error occurred while fetching events, please try again later.');
        console.error('Error fetching events:', error);
    } finally {
        eventsLoading.value = false;
    }
};

onMounted(async () => {
    try {
        await fetchEvents();
        if (selectedEvent.value) {
            await loadTickets();
        }
    } catch (error) {
        console.error('Error initializing tickets page:', error);
        $toast.error('Failed to initialize the tickets page. Please refresh and try again.');
    }
});

watch(selectedEvent, async () => {
    if (selectedEvent.value) {
        pagination.value.current_page = 1;
        await loadTickets();
    }
});

watch(dateRange, (newDateRange) => {
    if (newDateRange && Array.isArray(newDateRange) && newDateRange.length === 2) {
        filters.value.dateFrom = dayjs(newDateRange[0]).format('YYYY-MM-DD');
        filters.value.dateTo = dayjs(newDateRange[1]).format('YYYY-MM-DD');
    } else {
        filters.value.dateFrom = '';
        filters.value.dateTo = '';
    }
});
</script>

<style lang="css" scoped></style>
